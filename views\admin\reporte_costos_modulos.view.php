<?php
#region region DOCS

/** @var array $reporte_costos_modulos */
/** @var int|null $filtro_proyecto_id */
/** @var string|null $nombre_proyecto_filtro */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */
/** @var float $total_costo_usd */
/** @var int $total_n_mensajes */
/** @var float $total_costo_mensajes */
/** @var array $subtotales_por_modulo */

#endregion DOCS
?>

<!DOCTYPE html>
<html lang="es" class="dark-mode">

<head>
	<meta charset="utf-8" />
	<title>Reporte de Costos por Módulo | <?php echo APP_NAME; ?></title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
	<meta content="" name="description" />
	<meta content="" name="author" />

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>

<body>
	<!-- BEGIN #loader -->
	<div id="loader" class="app-loader">
		<span class="spinner"></span>
	</div>
	<!-- END #loader -->

	<!-- BEGIN #app -->
	<div id="app" class="app app-header-fixed app-sidebar-fixed">
		<!-- #header -->
		<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

		<!-- #sidebar -->
		<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php #region region PAGE HEADER ?>
			<div class="d-flex align-items-center mb-3">
				<div>
					<h4 class="mb-0">Reporte de Costos por Módulo<?php if ($nombre_proyecto_filtro): ?> - <?php echo htmlspecialchars($nombre_proyecto_filtro); ?><?php endif; ?></h4>
					<p class="mb-0 text-muted">Análisis de costos agrupados por módulo y agente</p>
				</div>
				<div class="ms-auto">
					<a href="lproyectos" class="btn btn-outline-primary"><i class="fa fa-folder fa-fw me-1"></i> Ver Proyectos</a>
				</div>
			</div>

			<hr>
			<?php #endregion PAGE HEADER ?>

			<?php if (!$filtro_proyecto_id && empty($reporte_costos_modulos)): ?>
				<div class="alert alert-info">
					<i class="fa fa-info-circle me-2"></i>
					Para consultar el reporte de costos por módulo, debe acceder desde la página de proyectos usando el botón "Reporte de costos por módulo".
				</div>
			<?php else: ?>

			<?php if (empty($reporte_costos_modulos)): ?>
				<div class="alert alert-warning">
					<i class="fa fa-exclamation-triangle me-2"></i>
					No se encontraron registros de tareas terminadas<?php if ($nombre_proyecto_filtro): ?> para el proyecto "<?php echo htmlspecialchars($nombre_proyecto_filtro); ?>"<?php endif; ?>.
				</div>
			<?php endif; ?>

			<?php if (!empty($reporte_costos_modulos)): ?>
			<?php #region region PANEL REPORTE COSTOS MODULOS ?>
			<div class="panel panel-inverse mt-3 no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">
						Reporte de Costos por Módulo
						<?php if ($nombre_proyecto_filtro): ?>
							- <?php echo htmlspecialchars($nombre_proyecto_filtro); ?>
						<?php endif; ?>
						(<?php echo count($reporte_costos_modulos); ?> registros)
					</h4>
				</div>
				<!-- BEGIN PANEL body -->
				<div class="table-nowrap" style="overflow: auto">
					<?php #region region TABLE REPORTE COSTOS MODULOS ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th>Módulo</th>
							<th>Agente</th>
							<th class="text-end">Costo USD</th>
							<th class="text-center">N° Mensajes</th>
							<th class="text-end">Costo Total Mensajes</th>
						</tr>
						</thead>
						<tbody class="fs-12px" id="reporte-costos-modulos-table-body">
						<?php 
						$current_module = null;
						foreach ($reporte_costos_modulos as $registro): 
							$modulo = $registro['modulo_descripcion'];
							
							// Show subtotal row when module changes
							if ($current_module !== null && $current_module !== $modulo && isset($subtotales_por_modulo[$current_module])): 
								$subtotal = $subtotales_por_modulo[$current_module];
						?>
							<tr class="table-secondary fw-bold">
								<td colspan="2" class="text-start">Subtotal <?php echo htmlspecialchars($current_module); ?>:</td>
								<td class="text-end">$<?php echo number_format($subtotal['costo_usd'], 2); ?></td>
								<td class="text-center"><?php echo number_format($subtotal['n_mensajes']); ?></td>
								<td class="text-end">$<?php echo number_format($subtotal['costo_total_mensajes'], 0, ',', '.'); ?></td>
							</tr>
						<?php 
							endif;
							$current_module = $modulo;
						?>
							<tr>
								<td class="align-middle"><?php echo htmlspecialchars($registro['modulo_descripcion'] ?? 'Sin Módulo'); ?></td>
								<td class="align-middle"><?php echo htmlspecialchars($registro['agente_descripcion'] ?? ''); ?></td>
								<td class="text-end align-middle">$<?php echo number_format($registro['costo_usd'] ?? 0, 2); ?></td>
								<td class="text-center align-middle"><?php echo number_format($registro['n_mensajes'] ?? 0); ?></td>
								<td class="text-end align-middle">
									<?php if (($registro['n_mensajes'] ?? 0) > 0): ?>
										$<?php echo number_format($registro['costo_total_mensajes'] ?? 0, 0, ',', '.'); ?>
									<?php else: ?>
										-
									<?php endif; ?>
								</td>
							</tr>
						<?php endforeach; ?>
						
						<?php // Show subtotal for the last module
						if ($current_module !== null && isset($subtotales_por_modulo[$current_module])): 
							$subtotal = $subtotales_por_modulo[$current_module];
						?>
							<tr class="table-secondary fw-bold">
								<td colspan="2" class="text-start">Subtotal <?php echo htmlspecialchars($current_module); ?>:</td>
								<td class="text-end">$<?php echo number_format($subtotal['costo_usd'], 2); ?></td>
								<td class="text-center"><?php echo number_format($subtotal['n_mensajes']); ?></td>
								<td class="text-end">$<?php echo number_format($subtotal['costo_total_mensajes'], 0, ',', '.'); ?></td>
							</tr>
						<?php endif; ?>
						</tbody>
						<tfoot>
						<tr class="table-info fw-bold">
							<td colspan="2" class="text-start">Totales Generales:</td>
							<td class="text-end">$<?php echo number_format($total_costo_usd, 2); ?></td>
							<td class="text-center"><?php echo number_format($total_n_mensajes); ?></td>
							<td class="text-end">$<?php echo number_format($total_costo_mensajes, 0, ',', '.'); ?></td>
						</tr>
						</tfoot>
					</table>
					<?php #endregion TABLE REPORTE COSTOS MODULOS ?>
				</div>
				<!-- END PANEL body -->
			</div>
			<?php #endregion PANEL REPORTE COSTOS MODULOS ?>
			<?php endif; ?>

			<?php endif; ?>

		</div>
		<!-- END #content -->

		<!-- BEGIN scroll-top-btn -->
		<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
		<!-- END scroll-top-btn -->
	</div>
	<!-- END #app -->

	<?php #region region JS ?>
	<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
	<?php #endregion JS ?>

</body>
</html>
