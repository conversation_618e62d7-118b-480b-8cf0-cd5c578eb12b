<?php
#region region DOCS

/** @var array $reporte_costos */
/** @var int|null $filtro_proyecto_id */
/** @var string|null $filtro_fecha_inicio */
/** @var string|null $filtro_fecha_fin */
/** @var string|null $nombre_proyecto_filtro */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */
/** @var float $total_costo_usd */
/** @var int $total_n_mensajes */
/** @var float $total_costo_mensajes */

#endregion DOCS
?>

<!DOCTYPE html>
<html lang="es" class="dark-mode">

<head>
	<meta charset="utf-8" />
	<title>Reporte de Costos por Tarea | <?php echo APP_NAME; ?></title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
	<meta content="" name="description" />
	<meta content="" name="author" />

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<link href="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
	<?php #endregion HEAD ?>
</head>

<body>
	<!-- BEGIN #loader -->
	<div id="loader" class="app-loader">
		<span class="spinner"></span>
	</div>
	<!-- END #loader -->

	<!-- BEGIN #app -->
	<div id="app" class="app app-header-fixed app-sidebar-fixed">
		<!-- #header -->
		<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

		<!-- #sidebar -->
		<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php #region region PAGE HEADER ?>
			<div class="d-flex align-items-center mb-3">
				<div>
					<h4 class="mb-0">Reporte de Costos por Tarea<?php if ($nombre_proyecto_filtro): ?> - <?php echo htmlspecialchars($nombre_proyecto_filtro); ?><?php endif; ?></h4>
					<p class="mb-0 text-muted">Análisis de costos de tareas terminadas del sistema</p>
				</div>
				<div class="ms-auto">
					<a href="lproyectos" class="btn btn-outline-primary"><i class="fa fa-folder fa-fw me-1"></i> Ver Proyectos</a>
				</div>
			</div>

			<hr>
			<?php #endregion PAGE HEADER ?>

			<?php if (!$filtro_proyecto_id): ?>
				<div class="alert alert-info">
					<i class="fa fa-info-circle me-2"></i>
					Para consultar el reporte de costos, debe acceder desde la página de proyectos usando el botón "Reporte de costos por tarea".
				</div>
			<?php else: ?>

			<?php #region region FILTERS ?>
			<div class="panel panel-inverse no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Filtros de Búsqueda</h4>
				</div>
				<div class="panel-body">
					<form method="POST" action="reporte-costos-tareas" class="row g-3">
						<div class="col-md-3">
							<label for="fecha_inicio" class="form-label">Fecha Inicio</label>
							<div class="input-group">
								<input type="text" class="form-control datepicker" id="fecha_inicio" name="fecha_inicio"
								       value="<?php echo htmlspecialchars($filtro_fecha_inicio ?? ''); ?>"
								       autocomplete="off" placeholder="yyyy-mm-dd">
								<span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
							</div>
						</div>
						<div class="col-md-3">
							<label for="fecha_fin" class="form-label">Fecha Fin</label>
							<div class="input-group">
								<input type="text" class="form-control datepicker" id="fecha_fin" name="fecha_fin"
								       value="<?php echo htmlspecialchars($filtro_fecha_fin ?? ''); ?>"
								       autocomplete="off" placeholder="yyyy-mm-dd">
								<span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
							</div>
						</div>
						<div class="col-md-6 d-flex align-items-end">
							<button type="submit" class="btn btn-primary me-2">
								<i class="fa fa-search me-1"></i>Buscar
							</button>
							<a href="reporte-costos-tareas" class="btn btn-outline-secondary">
								<i class="fa fa-times me-1"></i>Limpiar
							</a>
						</div>
					</form>
				</div>
			</div>
			<?php #endregion FILTERS ?>

			<?php if (($filtro_fecha_inicio || $filtro_fecha_fin) && empty($reporte_costos)): ?>
				<div class="alert alert-warning">
					<i class="fa fa-exclamation-triangle me-2"></i>
					No se encontraron registros con los filtros aplicados.
				</div>
			<?php endif; ?>

			<?php if (!empty($reporte_costos)): ?>
			<?php #region region PANEL REPORTE COSTOS ?>
			<div class="panel panel-inverse mt-3 no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">
						Reporte de Costos por Tarea
						<?php if ($nombre_proyecto_filtro): ?>
							- <?php echo htmlspecialchars($nombre_proyecto_filtro); ?>
						<?php endif; ?>
						(<?php echo count($reporte_costos); ?> registros)
					</h4>
				</div>
				<!-- BEGIN PANEL body -->
				<div class="table-nowrap" style="overflow: auto">
					<?php #region region TABLE REPORTE COSTOS ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="text-center">Fecha Terminación</th>
							<th>Sprint</th>
							<th>Módulo</th>
							<th>Tarea</th>
							<th>Agente</th>
							<th class="text-end">Costo USD</th>
							<th class="text-center">N° Mensajes</th>
							<th class="text-end">Costo Total Mensajes</th>
						</tr>
						</thead>
						<tbody class="fs-12px" id="reporte-costos-table-body">
						<?php foreach ($reporte_costos as $registro): ?>
							<tr>
								<td class="text-center align-middle"><?php
									$fecha = $registro['fecha_terminacion'] ?? '';
									echo $fecha ? date('Y-m-d', strtotime($fecha)) : '';
								?></td>
								<td class="align-middle"><?php echo htmlspecialchars($registro['sprint_descripcion'] ?? 'N/A'); ?></td>
								<td class="align-middle"><?php echo htmlspecialchars($registro['modulo_descripcion'] ?? 'Sin Módulo'); ?></td>
								<td class="align-middle"><?php echo htmlspecialchars($registro['tarea_descripcion'] ?? ''); ?></td>
								<td class="align-middle"><?php echo htmlspecialchars($registro['agente_descripcion'] ?? ''); ?></td>
								<td class="text-end align-middle">$<?php echo number_format($registro['costo_usd'] ?? 0, 2); ?></td>
								<td class="text-center align-middle"><?php echo number_format($registro['n_mensajes'] ?? 0); ?></td>
								<td class="text-end align-middle"><?php echo number_format($registro['costo_total_mensajes'] ?? 0, 0, ',', '.'); ?></td>
							</tr>
						<?php endforeach; ?>
						</tbody>
						<tfoot>
						<tr class="table-info fw-bold">
							<td colspan="5" class="text-start">Totales:</td>
							<td class="text-end">$<?php echo number_format($total_costo_usd, 2); ?></td>
							<td class="text-center"><?php echo number_format($total_n_mensajes); ?></td>
							<td class="text-end"><?php echo number_format($total_costo_mensajes, 0, ',', '.'); ?></td>
						</tr>
						</tfoot>
					</table>
					<?php #endregion TABLE REPORTE COSTOS ?>
				</div>
				<!-- END PANEL body -->
			</div>
			<?php #endregion PANEL REPORTE COSTOS ?>
			<?php endif; ?>

			<?php endif; ?>

		</div>
		<!-- END #content -->

		<!-- BEGIN scroll-top-btn -->
		<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
		<!-- END scroll-top-btn -->
	</div>
	<!-- END #app -->

	<?php #region region JS ?>
	<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
	<script src="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
	<script src="<?php echo RUTA_RESOURCES ?>js/datepickerini.js"></script>
	<?php #endregion JS ?>

</body>
</html>
